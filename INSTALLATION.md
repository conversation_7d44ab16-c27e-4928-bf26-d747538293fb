# Hướng dẫn cài đặt chi tiết MoMo Payment Gateway

## Chuẩn bị

### 1. <PERSON><PERSON><PERSON> tra yêu cầu hệ thống
```bash
# Kiểm tra phiên bản PHP
php -v

# Kiểm tra cURL extension
php -m | grep curl

# Kiểm tra SSL certificate
curl -I https://yourdomain.com
```

### 2. Backup WHMCS
```bash
# Backup database
mysqldump -u username -p database_name > whmcs_backup.sql

# Backup files
tar -czf whmcs_files_backup.tar.gz /path/to/whmcs/
```

## Cài đặt từng bước

### Bước 1: Upload module files

#### Cách 1: Upload qua FTP/SFTP
```bash
# Upload files vào thư mục WHMCS
scp -r modules/ user@server:/path/to/whmcs/
```

#### Cách 2: Upload qua cPanel File Manager
1. <PERSON><PERSON><PERSON> nhập cPanel
2. Mở File Manager
3. Navigate đến thư mục WHMCS
4. Upload và extract files

### Bước 2: Thiết lập permissions
```bash
# Set permissions cho callback file
chmod 644 modules/gateways/callback/momo.php
chmod 644 modules/gateways/momo.php
```

### Bước 3: Cấu hình WHMCS

#### 3.1 Kích hoạt module
1. Login WHMCS Admin: `https://yourdomain.com/admin/`
2. Setup → Payments → Payment Gateways
3. Tìm "MoMo Payment Gateway"
4. Click để kích hoạt

#### 3.2 Cấu hình thông số

**Test Environment:**
```
Partner Code: MOMOBKUN20180529
Access Key: klm05TvNBzhg7h7j
Secret Key: at67qH6mk8w5Y1nAyMoYKMWACiEi2bsa
Test Mode: Yes
Store Name: Your Store Name
Store ID: (optional)
```

**Production Environment:**
```
Partner Code: [Your Production Partner Code]
Access Key: [Your Production Access Key]
Secret Key: [Your Production Secret Key]
Test Mode: No
Store Name: [Your Store Name]
Store ID: [Your Store ID]
```

### Bước 4: Cấu hình MoMo Dashboard

#### 4.1 Đăng nhập MoMo Developer Portal
- URL: https://developers.momo.vn/
- Tạo tài khoản nếu chưa có

#### 4.2 Tạo ứng dụng
1. Tạo ứng dụng mới
2. Chọn loại: "Payment Gateway"
3. Điền thông tin ứng dụng

#### 4.3 Cấu hình Webhook URLs
```
IPN URL: https://yourdomain.com/modules/gateways/callback/momo.php
Return URL: https://yourdomain.com/modules/gateways/callback/momo.php
```

### Bước 5: Test cài đặt

#### 5.1 Test cơ bản
1. Tạo invoice test
2. Chọn MoMo payment
3. Kiểm tra hiển thị form thanh toán

#### 5.2 Test thanh toán
1. Sử dụng MoMo Test App
2. Thực hiện thanh toán test
3. Kiểm tra callback

#### 5.3 Kiểm tra logs
```
WHMCS Admin → Utilities → Logs → Gateway Log
```

## Xử lý sự cố cài đặt

### Lỗi 1: "Module Not Found"
**Nguyên nhân:** File module không đúng vị trí
**Giải pháp:**
```bash
# Kiểm tra cấu trúc thư mục
ls -la modules/gateways/momo.php
ls -la modules/gateways/callback/momo.php
```

### Lỗi 2: "Permission Denied"
**Nguyên nhân:** Không có quyền truy cập file
**Giải pháp:**
```bash
# Set permissions
chmod 644 modules/gateways/momo.php
chmod 644 modules/gateways/callback/momo.php
chown www-data:www-data modules/gateways/momo.php
```

### Lỗi 3: "cURL Error"
**Nguyên nhân:** cURL không được cài đặt hoặc cấu hình
**Giải pháp:**
```bash
# Ubuntu/Debian
sudo apt-get install php-curl

# CentOS/RHEL
sudo yum install php-curl

# Restart web server
sudo service apache2 restart
```

### Lỗi 4: "SSL Certificate Error"
**Nguyên nhân:** SSL certificate không hợp lệ
**Giải pháp:**
1. Cài đặt SSL certificate hợp lệ
2. Hoặc tạm thời disable SSL verify (chỉ cho test):
```php
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
```

## Cấu hình nâng cao

### 1. Custom CSS cho payment form
Thêm vào theme CSS:
```css
.momo-payment-container {
    max-width: 800px;
    margin: 0 auto;
}

.payment-option {
    transition: transform 0.2s;
}

.payment-option:hover {
    transform: translateY(-2px);
}
```

### 2. Webhook security
Thêm IP whitelist cho callback:
```php
$allowedIPs = ['*************', '*************']; // MoMo IPs
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs)) {
    die('Access denied');
}
```

### 3. Custom logging
```php
// Thêm vào callback file
function customLog($message) {
    $log = date('Y-m-d H:i:s') . " - " . $message . "\n";
    file_put_contents('momo_custom.log', $log, FILE_APPEND);
}
```

## Checklist sau cài đặt

- [ ] Module hiển thị trong Payment Gateways
- [ ] Cấu hình thông số đầy đủ
- [ ] Test mode hoạt động
- [ ] Callback URL accessible
- [ ] SSL certificate hợp lệ
- [ ] Logs ghi nhận đúng
- [ ] Test payment thành công
- [ ] Production credentials ready

## Bảo trì

### Cập nhật module
1. Backup files hiện tại
2. Upload files mới
3. Test trên staging trước
4. Deploy lên production

### Monitor logs
```bash
# Theo dõi logs realtime
tail -f /path/to/whmcs/logs/gateway.log | grep "MoMo"
```

### Backup định kỳ
```bash
# Backup hàng ngày
0 2 * * * /usr/bin/mysqldump -u user -p'password' whmcs > /backup/whmcs_$(date +\%Y\%m\%d).sql
```
