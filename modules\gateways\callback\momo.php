<?php
/**
 * MoMo Payment Gateway Callback Handler for WHMCS
 *
 * This file handles the callback/IPN from MoMo payment gateway
 * 
 * <AUTHOR> Name
 * @copyright Copyright (c) 2025
 * @license MIT License
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    die("Module Not Activated");
}

// Retrieve data returned in payment gateway callback
$success = false;
$transactionId = '';
$paymentAmount = '';
$paymentFee = '';
$hash = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle IPN (Instant Payment Notification)
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        // Try to get data from POST parameters
        $data = $_POST;
    }
    
    logActivity('MoMo Payment Gateway - IPN Received: ' . json_encode($data));
    
} else {
    // Handle redirect callback
    $data = $_GET;
    logActivity('MoMo Payment Gateway - Redirect Callback: ' . json_encode($data));
}

// Validate required parameters
if (!isset($data['orderId']) || !isset($data['resultCode']) || !isset($data['signature'])) {
    logActivity('MoMo Payment Gateway - Missing required parameters');
    die('Invalid callback data');
}

// Extract invoice ID from order ID
$orderId = $data['orderId'];
$invoiceId = '';
if (strpos($orderId, '_') !== false) {
    $parts = explode('_', $orderId);
    $invoiceId = $parts[0];
} else {
    $invoiceId = $orderId;
}

// Verify signature
$secretKey = $gatewayParams['secretKey'];
$accessKey = $gatewayParams['accessKey'];

// Prepare data for signature verification
$verifyData = array(
    'accessKey' => $accessKey,
    'amount' => $data['amount'],
    'extraData' => isset($data['extraData']) ? $data['extraData'] : '',
    'message' => isset($data['message']) ? $data['message'] : '',
    'orderId' => $data['orderId'],
    'orderInfo' => isset($data['orderInfo']) ? $data['orderInfo'] : '',
    'orderType' => isset($data['orderType']) ? $data['orderType'] : 'momo_wallet',
    'partnerCode' => $data['partnerCode'],
    'payType' => isset($data['payType']) ? $data['payType'] : '',
    'requestId' => isset($data['requestId']) ? $data['requestId'] : $data['orderId'],
    'responseTime' => isset($data['responseTime']) ? $data['responseTime'] : time(),
    'resultCode' => $data['resultCode'],
    'transId' => isset($data['transId']) ? $data['transId'] : '',
    'signature' => $data['signature']
);

// Verify signature
if (!momo_verifySignature($verifyData, $secretKey)) {
    logActivity('MoMo Payment Gateway - Invalid signature for order: ' . $orderId);
    die('Invalid signature');
}

// Check transaction status
if ($data['resultCode'] == 0) {
    // Payment successful
    $success = true;
    $transactionId = isset($data['transId']) ? $data['transId'] : $orderId;
    $paymentAmount = $data['amount'] / 1000; // Convert from VND to base currency
    $paymentFee = isset($data['userFee']) ? $data['userFee'] / 1000 : 0;
    
    logActivity('MoMo Payment Gateway - Payment successful for invoice: ' . $invoiceId . ', Transaction ID: ' . $transactionId);
    
} else {
    // Payment failed
    $success = false;
    $errorMessage = isset($data['message']) ? $data['message'] : 'Payment failed';
    
    logActivity('MoMo Payment Gateway - Payment failed for invoice: ' . $invoiceId . ', Error: ' . $errorMessage);
}

/**
 * Validate Callback Invoice ID.
 *
 * Checks invoice ID is a valid invoice number. Note it will count an
 * invoice in any status as valid.
 *
 * Performs a die upon encountering an invalid Invoice ID.
 *
 * Returns a normalised invoice ID.
 *
 * @param int $invoiceId Invoice ID
 * @param string $gatewayName Gateway Name
 */
$invoiceId = checkCbInvoiceID($invoiceId, $gatewayParams['name']);

/**
 * Check Callback Transaction ID.
 *
 * Performs a check for any existing transactions with the same given
 * transaction number.
 *
 * Performs a die upon encountering a duplicate.
 *
 * @param string $transactionId Unique Transaction ID
 */
if ($success) {
    checkCbTransID($transactionId);
}

/**
 * Log Transaction.
 *
 * Add an entry to the Gateway Log for debugging purposes.
 *
 * The debug data can be a string or an array. In the case of an
 * array it will be
 *
 * @param string $gatewayName        Display label
 * @param string|array $debugData    Data to log
 * @param string $transactionStatus  Status
 */
logTransaction($gatewayParams['name'], $data, $success ? "Success" : "Failure");

if ($success) {
    /**
     * Add Invoice Payment.
     *
     * Applies a payment transaction entry to the given invoice ID.
     *
     * @param int $invoiceId         Invoice ID
     * @param string $transactionId  Transaction ID
     * @param float $paymentAmount   Amount paid (defaults to full balance)
     * @param float $paymentFee      Payment fee (optional)
     * @param string $gatewayModule  Gateway module name
     */
    addInvoicePayment(
        $invoiceId,
        $transactionId,
        $paymentAmount,
        $paymentFee,
        $gatewayModuleName
    );
    
    // Redirect to invoice if this is a redirect callback
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        header('Location: ' . $CONFIG['SystemURL'] . '/viewinvoice.php?id=' . $invoiceId . '&paymentsuccess=true');
        exit;
    }
    
} else {
    // Redirect to invoice with error if this is a redirect callback
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        header('Location: ' . $CONFIG['SystemURL'] . '/viewinvoice.php?id=' . $invoiceId . '&paymentfailed=true');
        exit;
    }
}

// For IPN, return success response
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    http_response_code(200);
    echo "OK";
}

/**
 * Verify signature from MoMo callback
 *
 * @param array $data
 * @param string $secretKey
 * @return bool
 */
function momo_verifySignature($data, $secretKey)
{
    $rawHash = "accessKey=" . $data['accessKey'] . 
               "&amount=" . $data['amount'] . 
               "&extraData=" . $data['extraData'] . 
               "&message=" . $data['message'] . 
               "&orderId=" . $data['orderId'] . 
               "&orderInfo=" . $data['orderInfo'] . 
               "&orderType=" . $data['orderType'] . 
               "&partnerCode=" . $data['partnerCode'] . 
               "&payType=" . $data['payType'] . 
               "&requestId=" . $data['requestId'] . 
               "&responseTime=" . $data['responseTime'] . 
               "&resultCode=" . $data['resultCode'] . 
               "&transId=" . $data['transId'];

    $signature = hash_hmac("sha256", $rawHash, $secretKey);
    
    return $signature === $data['signature'];
}
