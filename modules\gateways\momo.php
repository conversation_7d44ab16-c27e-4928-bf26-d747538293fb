<?php
/**
 * MoMo Payment Gateway Module for WHMCS
 *
 * This module allows you to accept payments via MoMo e-wallet in Vietnam
 * 
 * <AUTHOR> Name
 * @copyright Copyright (c) 2025
 * @license MIT License
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Define module related meta data.
 *
 * @return array
 */
function momo_MetaData()
{
    return array(
        'DisplayName' => 'MoMo Payment Gateway',
        'APIVersion' => '1.1',
        'DisableLocalCredtCardInput' => true,
        'TokenisedStorage' => false,
    );
}

/**
 * Define gateway configuration options.
 *
 * @return array
 */
function momo_config()
{
    return array(
        'FriendlyName' => array(
            'Type' => 'System',
            'Value' => 'MoMo Payment Gateway',
        ),
        'partnerCode' => array(
            'FriendlyName' => 'Partner Code',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your MoMo Partner Code here',
        ),
        'accessKey' => array(
            'FriendlyName' => 'Access Key',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your MoMo Access Key here',
        ),
        'secretKey' => array(
            'FriendlyName' => 'Secret Key',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your MoMo Secret Key here',
        ),
        'testMode' => array(
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Tick to enable test mode',
        ),
        'storeName' => array(
            'FriendlyName' => 'Store Name',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your store name',
        ),
        'storeId' => array(
            'FriendlyName' => 'Store ID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your store ID (optional)',
        ),
    );
}

/**
 * Payment link.
 *
 * @param array $params Payment Gateway Module Parameters
 *
 * @return string
 */
function momo_link($params)
{
    // Gateway Configuration Parameters
    $partnerCode = $params['partnerCode'];
    $accessKey = $params['accessKey'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];
    $storeName = $params['storeName'];
    $storeId = $params['storeId'];

    // Invoice Parameters
    $invoiceId = $params['invoiceid'];
    $description = $params["description"];
    $amount = $params['amount'];
    $currencyCode = $params['currency'];

    // Client Parameters
    $firstname = $params['clientdetails']['firstname'];
    $lastname = $params['clientdetails']['lastname'];
    $email = $params['clientdetails']['email'];
    $address1 = $params['clientdetails']['address1'];
    $address2 = $params['clientdetails']['address2'];
    $city = $params['clientdetails']['city'];
    $state = $params['clientdetails']['state'];
    $postcode = $params['clientdetails']['postcode'];
    $country = $params['clientdetails']['country'];
    $phone = $params['clientdetails']['phonenumber'];

    // System Parameters
    $companyName = $params['companyname'];
    $systemUrl = $params['systemurl'];
    $returnUrl = $params['returnurl'];
    $langPayNow = $params['langpaynow'];
    $moduleDisplayName = $params['name'];
    $moduleName = $params['paymentmethod'];
    $whmcsVersion = $params['whmcsVersion'];

    // Build MoMo payment request
    $endpoint = $testMode ? "https://test-payment.momo.vn/v2/gateway/api/create" : "https://payment.momo.vn/v2/gateway/api/create";
    
    $orderId = $invoiceId . '_' . time();
    $requestId = $orderId;
    $orderInfo = "Payment for Invoice #" . $invoiceId;
    $redirectUrl = $systemUrl . 'modules/gateways/callback/momo.php';
    $ipnUrl = $systemUrl . 'modules/gateways/callback/momo.php';
    $requestType = "captureWallet";
    $extraData = "";

    // Convert amount to VND (assuming base currency might be different)
    $amountVND = intval($amount * 1000); // Convert to VND if needed

    // Create signature
    $rawHash = "accessKey=" . $accessKey . 
               "&amount=" . $amountVND . 
               "&extraData=" . $extraData . 
               "&ipnUrl=" . $ipnUrl . 
               "&orderId=" . $orderId . 
               "&orderInfo=" . $orderInfo . 
               "&partnerCode=" . $partnerCode . 
               "&redirectUrl=" . $redirectUrl . 
               "&requestId=" . $requestId . 
               "&requestType=" . $requestType;

    $signature = hash_hmac("sha256", $rawHash, $secretKey);

    // Prepare request data
    $data = array(
        'partnerCode' => $partnerCode,
        'storeName' => $storeName,
        'storeId' => $storeId,
        'requestId' => $requestId,
        'amount' => $amountVND,
        'orderId' => $orderId,
        'orderInfo' => $orderInfo,
        'redirectUrl' => $redirectUrl,
        'ipnUrl' => $ipnUrl,
        'requestType' => $requestType,
        'extraData' => $extraData,
        'signature' => $signature,
        'lang' => 'vi'
    );

    // Make API call to MoMo
    $result = momo_execPostRequest($endpoint, json_encode($data));
    $jsonResult = json_decode($result, true);

    if (isset($jsonResult['payUrl']) && $jsonResult['resultCode'] == 0) {
        // Store transaction info for later verification
        momo_logTransaction($orderId, $invoiceId, $amountVND, $jsonResult);

        // Create enhanced payment form with multiple options
        $htmlOutput = momo_generatePaymentForm($jsonResult, $langPayNow, $amountVND);

        return $htmlOutput;
    } else {
        // Handle error
        $errorMsg = isset($jsonResult['message']) ? $jsonResult['message'] : 'Unknown error occurred';
        return '<div class="alert alert-danger">Payment Error: ' . $errorMsg . '</div>';
    }
}

/**
 * Execute POST request to MoMo API
 *
 * @param string $url
 * @param string $data
 * @return string
 */
function momo_execPostRequest($url, $data)
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $result = curl_exec($ch);

    if (curl_error($ch)) {
        logActivity('MoMo Payment Gateway - cURL Error: ' . curl_error($ch));
        return false;
    }

    curl_close($ch);
    return $result;
}

/**
 * Log transaction for debugging and verification
 *
 * @param string $orderId
 * @param string $invoiceId
 * @param int $amount
 * @param array $response
 */
function momo_logTransaction($orderId, $invoiceId, $amount, $response)
{
    $logData = array(
        'orderId' => $orderId,
        'invoiceId' => $invoiceId,
        'amount' => $amount,
        'response' => $response,
        'timestamp' => date('Y-m-d H:i:s')
    );

    logActivity('MoMo Payment Gateway - Transaction Created: ' . json_encode($logData));
}

/**
 * Verify signature from MoMo callback
 *
 * @param array $data
 * @param string $secretKey
 * @return bool
 */
function momo_verifySignature($data, $secretKey)
{
    $rawHash = "accessKey=" . $data['accessKey'] .
               "&amount=" . $data['amount'] .
               "&extraData=" . $data['extraData'] .
               "&message=" . $data['message'] .
               "&orderId=" . $data['orderId'] .
               "&orderInfo=" . $data['orderInfo'] .
               "&orderType=" . $data['orderType'] .
               "&partnerCode=" . $data['partnerCode'] .
               "&payType=" . $data['payType'] .
               "&requestId=" . $data['requestId'] .
               "&responseTime=" . $data['responseTime'] .
               "&resultCode=" . $data['resultCode'] .
               "&transId=" . $data['transId'];

    $signature = hash_hmac("sha256", $rawHash, $secretKey);

    return $signature === $data['signature'];
}

/**
 * Generate enhanced payment form with multiple MoMo payment options
 *
 * @param array $momoResponse
 * @param string $langPayNow
 * @param int $amount
 * @return string
 */
function momo_generatePaymentForm($momoResponse, $langPayNow, $amount)
{
    $payUrl = $momoResponse['payUrl'];
    $qrCodeUrl = isset($momoResponse['qrCodeUrl']) ? $momoResponse['qrCodeUrl'] : '';
    $deeplink = isset($momoResponse['deeplink']) ? $momoResponse['deeplink'] : '';

    $formattedAmount = number_format($amount) . ' VND';

    $htmlOutput = '
    <div class="momo-payment-container" style="border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 10px 0; background: #f9f9f9;">
        <div class="momo-header" style="text-align: center; margin-bottom: 20px;">
            <img src="https://developers.momo.vn/v3/assets/images/square-logo.svg" alt="MoMo" style="height: 40px; margin-right: 10px;">
            <h4 style="display: inline-block; margin: 0; color: #d82d8b;">Thanh toán với MoMo</h4>
            <p style="margin: 5px 0; color: #666;">Số tiền: <strong>' . $formattedAmount . '</strong></p>
        </div>

        <div class="payment-options" style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: center;">
            <!-- Web Payment Option -->
            <div class="payment-option" style="flex: 1; min-width: 200px; max-width: 300px;">
                <div style="border: 1px solid #d82d8b; border-radius: 6px; padding: 15px; text-align: center; background: white;">
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-globe" style="font-size: 24px; color: #d82d8b;"></i>
                    </div>
                    <h5 style="margin: 10px 0; color: #333;">Thanh toán trên Web</h5>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">Chuyển đến trang thanh toán MoMo</p>
                    <form method="post" action="' . $payUrl . '" style="margin: 0;">
                        <input type="submit" value="' . $langPayNow . '" class="btn btn-primary" style="background: #d82d8b; border-color: #d82d8b; width: 100%;" />
                    </form>
                </div>
            </div>';

    // Add QR Code option if available
    if (!empty($qrCodeUrl)) {
        $htmlOutput .= '
            <!-- QR Code Payment Option -->
            <div class="payment-option" style="flex: 1; min-width: 200px; max-width: 300px;">
                <div style="border: 1px solid #d82d8b; border-radius: 6px; padding: 15px; text-align: center; background: white;">
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-qrcode" style="font-size: 24px; color: #d82d8b;"></i>
                    </div>
                    <h5 style="margin: 10px 0; color: #333;">Quét mã QR</h5>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">Mở app MoMo và quét mã QR</p>
                    <button type="button" class="btn btn-outline-primary" onclick="showQRCode(\'' . $qrCodeUrl . '\')" style="border-color: #d82d8b; color: #d82d8b; width: 100%;">
                        Hiển thị mã QR
                    </button>
                </div>
            </div>';
    }

    // Add App Deep Link option if available
    if (!empty($deeplink)) {
        $htmlOutput .= '
            <!-- App Payment Option -->
            <div class="payment-option" style="flex: 1; min-width: 200px; max-width: 300px;">
                <div style="border: 1px solid #d82d8b; border-radius: 6px; padding: 15px; text-align: center; background: white;">
                    <div style="margin-bottom: 10px;">
                        <i class="fa fa-mobile" style="font-size: 24px; color: #d82d8b;"></i>
                    </div>
                    <h5 style="margin: 10px 0; color: #333;">Mở App MoMo</h5>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">Mở trực tiếp ứng dụng MoMo</p>
                    <a href="' . $deeplink . '" class="btn btn-success" style="background: #28a745; border-color: #28a745; width: 100%; text-decoration: none; display: inline-block; padding: 8px 12px; border-radius: 4px; color: white;">
                        Mở App MoMo
                    </a>
                </div>
            </div>';
    }

    $htmlOutput .= '
        </div>

        <div class="payment-info" style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #ddd; text-align: center;">
            <p style="font-size: 12px; color: #666; margin: 0;">
                <i class="fa fa-shield" style="color: #28a745;"></i>
                Giao dịch được bảo mật bởi MoMo
            </p>
        </div>
    </div>';

    // Add QR Code modal and JavaScript
    if (!empty($qrCodeUrl)) {
        $htmlOutput .= momo_getQRCodeModal();
        $htmlOutput .= momo_getJavaScript();
    }

    return $htmlOutput;
}

/**
 * Get QR Code modal HTML
 *
 * @return string
 */
function momo_getQRCodeModal()
{
    return '
    <!-- QR Code Modal -->
    <div id="momoQRModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="position: relative; margin: 10% auto; padding: 20px; width: 300px; background-color: white; border-radius: 8px; text-align: center;">
            <span onclick="closeQRModal()" style="position: absolute; right: 10px; top: 10px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
            <h4 style="margin-top: 0; color: #d82d8b;">Quét mã QR để thanh toán</h4>
            <div id="qrcode" style="margin: 20px 0;"></div>
            <p style="font-size: 12px; color: #666;">Mở ứng dụng MoMo và quét mã QR này để thanh toán</p>
        </div>
    </div>';
}

/**
 * Get JavaScript for QR Code functionality
 *
 * @return string
 */
function momo_getJavaScript()
{
    return '
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        function showQRCode(qrData) {
            document.getElementById("momoQRModal").style.display = "block";

            // Clear previous QR code
            document.getElementById("qrcode").innerHTML = "";

            // Generate QR code
            QRCode.toCanvas(document.getElementById("qrcode"), qrData, {
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.M
            }, function (error) {
                if (error) {
                    console.error(error);
                    document.getElementById("qrcode").innerHTML = "<p>Không thể tạo mã QR</p>";
                }
            });
        }

        function closeQRModal() {
            document.getElementById("momoQRModal").style.display = "none";
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            var modal = document.getElementById("momoQRModal");
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }
    </script>';
}
