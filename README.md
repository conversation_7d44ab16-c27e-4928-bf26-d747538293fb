# MoMo Payment Gateway Module for WHMCS

Module thanh toán MoMo cho WHMCS, hỗ trợ thanh toán qua ví điện tử MoMo tại Việt Nam.

## Tính năng

- ✅ Tích hợp API MoMo v2 (captureWallet)
- ✅ Hỗ trợ thanh toán trên web, mobile và QR code
- ✅ Xử lý callback và IPN từ MoMo
- ✅ Xác thực chữ ký bảo mật
- ✅ Giao diện thanh toán thân thiện
- ✅ Hỗ trợ môi trường test và production
- ✅ Logging chi tiết cho debug

## Yêu cầu hệ thống

- WHMCS 7.0 trở lên
- PHP 7.0 trở lên
- cURL extension
- SSL certificate (bắt buộc cho production)

## Cài đặt

### Bước 1: Upload files

1. Upload thư mục `modules` vào thư mục gốc của WHMCS
2. <PERSON><PERSON><PERSON> bảo cấu trúc thư mục như sau:
```
/path/to/whmcs/
├── modules/
│   └── gateways/
│       ├── momo.php
│       └── callback/
│           └── momo.php
```

### Bước 2: Cấu hình trong WHMCS Admin

1. Đăng nhập vào WHMCS Admin
2. Vào **Setup** → **Payments** → **Payment Gateways**
3. Tìm và click **MoMo Payment Gateway**
4. Điền thông tin cấu hình:

#### Thông tin cấu hình

| Trường | Mô tả | Bắt buộc |
|--------|-------|----------|
| Partner Code | Mã đối tác từ MoMo | ✅ |
| Access Key | Access Key từ MoMo | ✅ |
| Secret Key | Secret Key từ MoMo | ✅ |
| Test Mode | Bật để sử dụng môi trường test | ❌ |
| Store Name | Tên cửa hàng hiển thị | ✅ |
| Store ID | Mã cửa hàng (tùy chọn) | ❌ |

### Bước 3: Lấy thông tin từ MoMo

#### Môi trường Test
1. Truy cập [MoMo Developer Portal](https://developers.momo.vn/)
2. Đăng ký tài khoản developer
3. Tạo ứng dụng test và lấy thông tin:
   - Partner Code
   - Access Key  
   - Secret Key

#### Môi trường Production
1. Liên hệ MoMo để đăng ký merchant
2. Hoàn thành quy trình KYC
3. Nhận thông tin production từ MoMo

## Cấu hình URL Callback

Trong cấu hình MoMo, bạn cần thiết lập:

- **IPN URL**: `https://yourdomain.com/modules/gateways/callback/momo.php`
- **Return URL**: `https://yourdomain.com/modules/gateways/callback/momo.php`

## Sử dụng

### Thanh toán cho khách hàng

1. Khách hàng chọn **MoMo Payment Gateway** khi thanh toán
2. Hệ thống hiển thị 3 tùy chọn thanh toán:
   - **Thanh toán trên Web**: Chuyển đến trang MoMo
   - **Quét mã QR**: Hiển thị QR code để quét
   - **Mở App MoMo**: Deep link mở trực tiếp app

### Luồng thanh toán

1. Khách hàng chọn phương thức thanh toán
2. WHMCS gọi API MoMo tạo giao dịch
3. Khách hàng thực hiện thanh toán trên MoMo
4. MoMo gửi callback về WHMCS
5. WHMCS cập nhật trạng thái hóa đơn

## Troubleshooting

### Lỗi thường gặp

#### 1. "Module Not Activated"
- **Nguyên nhân**: Module chưa được kích hoạt
- **Giải pháp**: Kích hoạt module trong WHMCS Admin

#### 2. "Invalid signature"
- **Nguyên nhân**: Secret Key không đúng hoặc dữ liệu bị thay đổi
- **Giải pháp**: Kiểm tra lại Secret Key và đảm bảo SSL

#### 3. "Payment Error: Unknown error occurred"
- **Nguyên nhân**: Lỗi kết nối API hoặc cấu hình sai
- **Giải pháp**: Kiểm tra logs và cấu hình API

### Kiểm tra logs

1. Vào **Utilities** → **Logs** → **Gateway Log** trong WHMCS Admin
2. Tìm entries có prefix "MoMo Payment Gateway"
3. Kiểm tra chi tiết lỗi và response từ API

### Test thanh toán

#### Môi trường Test
1. Bật **Test Mode** trong cấu hình
2. Sử dụng app **MoMo Test** (tải từ MoMo Developer)
3. Sử dụng tài khoản test được cung cấp

#### Thông tin test
- **Số điện thoại test**: Được cung cấp bởi MoMo
- **Mật khẩu test**: Được cung cấp bởi MoMo

## Bảo mật

### Khuyến nghị
- ✅ Sử dụng SSL certificate
- ✅ Bảo mật Secret Key
- ✅ Kiểm tra signature mọi callback
- ✅ Validate dữ liệu đầu vào
- ✅ Monitor logs thường xuyên

### Lưu ý quan trọng
- Không chia sẻ Secret Key
- Không disable signature verification
- Luôn sử dụng HTTPS cho callback URL

## Hỗ trợ

### Liên hệ MoMo
- **Website**: https://developers.momo.vn/
- **Email**: <EMAIL>
- **Hotline**: 1900 545 411

### Báo lỗi
Nếu gặp vấn đề với module, vui lòng:
1. Kiểm tra logs trong WHMCS
2. Đảm bảo cấu hình đúng
3. Liên hệ support với thông tin chi tiết

## Changelog

### Version 1.0.0
- Phiên bản đầu tiên
- Hỗ trợ API MoMo v2
- Giao diện thanh toán đa tùy chọn
- Xử lý callback và IPN

## License

MIT License - Xem file LICENSE để biết chi tiết.
